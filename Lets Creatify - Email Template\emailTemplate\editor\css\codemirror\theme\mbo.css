/****************************************************************/
/*   Based on mbonac<PERSON>'s Brackets mbo theme                      */
/*   https://github.com/mbonaci/global/blob/master/Mbo.tmTheme  */
/*   Create your own: http://tmtheme-editor.herokuapp.com       */
/****************************************************************/

.cm-s-mbo.CodeMirror {background: #363636; color: #ffffec;}
.cm-s-mbo div.CodeMirror-selected {background: #716C62 !important;}
.cm-s-mbo .CodeMirror-gutters {background: #4e4e4e; border-right: 0px;}
.cm-s-mbo .CodeMirror-guttermarker { color: white; }
.cm-s-mbo .CodeMirror-guttermarker-subtle { color: grey; }
.cm-s-mbo .CodeMirror-linenumber {color: #dadada;}
.cm-s-mbo .CodeMirror-cursor {border-left: 1px solid #ffffec !important;}

.cm-s-mbo span.cm-comment {color: #95958a;}
.cm-s-mbo span.cm-atom {color: #00a8c6;}
.cm-s-mbo span.cm-number {color: #00a8c6;}

.cm-s-mbo span.cm-property, .cm-s-mbo span.cm-attribute {color: #9ddfe9;}
.cm-s-mbo span.cm-keyword {color: #ffb928;}
.cm-s-mbo span.cm-string {color: #ffcf6c;}
.cm-s-mbo span.cm-string.cm-property {color: #ffffec;}

.cm-s-mbo span.cm-variable {color: #ffffec;}
.cm-s-mbo span.cm-variable-2 {color: #00a8c6;}
.cm-s-mbo span.cm-def {color: #ffffec;}
.cm-s-mbo span.cm-bracket {color: #fffffc; font-weight: bold;}
.cm-s-mbo span.cm-tag {color: #9ddfe9;}
.cm-s-mbo span.cm-link {color: #f54b07;}
.cm-s-mbo span.cm-error {border-bottom: #636363; color: #ffffec;}
.cm-s-mbo span.cm-qualifier {color: #ffffec;}

.cm-s-mbo .CodeMirror-activeline-background {background: #494b41 !important;}
.cm-s-mbo .CodeMirror-matchingbracket {color: #222 !important;}
.cm-s-mbo .CodeMirror-matchingtag {background: rgba(255, 255, 255, .37);}
